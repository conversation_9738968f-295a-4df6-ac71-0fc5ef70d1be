// src/store/authSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";
import { jwtDecode } from "jwt-decode";

interface AuthState {
    isAuthenticated: boolean;
    user: any | null;
    userAuthId: string | null;
    loading: boolean;
    error: string | null;
    loadingCaptcha: boolean;
    captcha: string;
}

const initialState: AuthState = {
    isAuthenticated: false,
    user: null,
    userAuthId: null,
    loading: false,
    error: null,
    loadingCaptcha: false,
    captcha: '',
};

// Async thunk to fetch the captcha
export const fetchCaptcha: any = createAsyncThunk(
    'auth/fetchCaptcha',
    async (_, thunkAPI) => {
        try {
            const response = await api.get('/captcha');
            localStorage.setItem("hashed_captcha_text", response.data.hashed_captcha_text);
            return response.data.captcha;
        } catch (error: any) {
            console.log(error)
            return thunkAPI.rejectWithValue(error.response?.data || 'Captcha fetch failed');
        }
    }
);

// Async thunk to login
export const login = createAsyncThunk(
    'auth/login',
    async (credentialsData: any, thunkAPI) => {
        try {
            const response = await api.post('/login', credentialsData);
            localStorage.setItem('TOKEN', response.data.token); // Save the token to localStorage
            localStorage.removeItem("hashed_captcha_text");
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Login failed');
        }
    }
);

// Async thunk to refresh token
export const refreshToken = createAsyncThunk(
    'auth/refreshToken',
    async (_, thunkAPI) => {
        try {
            const response = await api.post('/refresh-token');
            localStorage.setItem('TOKEN', response.data); // Save the new token to localStorage
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Token refresh failed');
        }
    }
);


export const checkAuthStatus = createAsyncThunk(
    'auth/checkAuthStatus',
    async (_, thunkAPI) => {
        const token = localStorage.getItem('TOKEN');
        if (token) {
            try {
                const decoded: any = jwtDecode(token);
                const currentTime = Date.now();

                // Vérification immédiate de l'expiration sans appeler l'API
                if (decoded.exp * 1000 < currentTime) {
                    localStorage.removeItem('TOKEN');
                    return thunkAPI.rejectWithValue('Token expired');
                }
                return { isAuthenticated: true, auth_data: decoded };

                // Si la validation du token local est OK, appelez l'API
                /*const response = await api.get('/verify-token');
                if (response.status === 200) {
                    return { isAuthenticated: true, auth_data: decoded };
                } else {
                    localStorage.removeItem('TOKEN');
                    return thunkAPI.rejectWithValue('Token is invalid');
                }*/
            } catch (error: any) {
                localStorage.removeItem('TOKEN');
                return thunkAPI.rejectWithValue('Failed to decode token');
            }
        }

        return thunkAPI.rejectWithValue('No token found');
    }
);


// Async thunk to get user auth ID from the token
export const getUserAuthId = createAsyncThunk(
    'auth/getUserAuthId',
    async (_, thunkAPI) => {
        const token = localStorage.getItem('TOKEN');
        if (token) {
            try {
                const decoded: any = jwtDecode(token);
                return decoded.id;
            } catch (error) {
                return thunkAPI.rejectWithValue('Failed to decode token');
            }
        }
        return thunkAPI.rejectWithValue('No token found');
    }
);

// Async thunk to logout
export const logout = createAsyncThunk(
    'auth/logout',
    async (_, thunkAPI) => {
        try {
            const response = await api.get('/logout');
            localStorage.removeItem('TOKEN'); // Remove the token from localStorage
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Logout failed');
        }
    }
);

// Async thunk to store subscriber step one
export const storeSubscriberStepOne = createAsyncThunk(
    'auth/storeSubscriberStepOne',
    async (subscriberData: any, thunkAPI) => {
        try {
            const response = await api.post('/store-subscriber-step-one', subscriberData);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Store subscriber step one failed');
        }
    }
);

// Async thunk to store subscriber step two
export const storeSubscriberStepTwo = createAsyncThunk(
    'auth/storeSubscriberStepTwo',
    async (subscriberData: any, thunkAPI) => {
        try {
            const response = await api.post('/store-subscriber-step-two', subscriberData);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Store subscriber step two failed');
        }
    }
);

// Async thunk to store subscriber step three
export const storeSubscriberStepThree = createAsyncThunk(
    'auth/storeSubscriberStepThree',
    async (subscriberData: any, thunkAPI) => {
        try {
            const response = await api.post('/store-subscriber-step-three', subscriberData);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Store subscriber step three failed');
        }
    }
);

// Async thunk to store subscriber step four
export const storeSubscriberStepFour = createAsyncThunk(
    'auth/storeSubscriberStepFour',
    async (subscriberData: any, thunkAPI) => {
        try {
            const response = await api.post('/store-subscriber-step-four', subscriberData);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Store subscriber step four failed');
        }
    }
);

// Async thunk to store subscriber final step 
export const storeSubscriber = createAsyncThunk(
    'auth/storeSubscriber',
    async (subscriberData: any, thunkAPI) => {
        try {
            const response = await api.post('/store-subscriber', subscriberData);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Store subscriber failed');
        }
    }
);

// Async thunk to reset password step one
export const resetPasswordStepOne = createAsyncThunk(
    'auth/resetPasswordStepOne',
    async (data: any, thunkAPI) => {
        try {
            const response = await api.post('/reset-password-step-one', data);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Reset password step one failed');
        }
    }
);

// Async thunk to reset password step two
export const resetPasswordStepTwo = createAsyncThunk(
    'auth/resetPasswordStepTwo',
    async (data: any, thunkAPI) => {
        try {
            const response = await api.post('/reset-password-step-two', data);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Reset password step two failed');
        }
    }
);

// Async thunk to reset password
export const resetPassword = createAsyncThunk(
    'auth/resetPassword',
    async (data: any, thunkAPI) => {
        try {
            const response = await api.post('/reset-password', data);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Reset password failed');
        }
    }
);

// Async thunk to update profile
export const updateProfile = createAsyncThunk(
    'auth/updateProfile',
    async (data: any, thunkAPI) => {
        try {
            const response = await api.post('/update-profile', data);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Update profile failed');
        }
    }
);

// Async thunk to update password
export const updatePassword = createAsyncThunk(
    'auth/updatePassword',
    async (data: any, thunkAPI) => {
        try {
            const response = await api.post('/update-password', data);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Update password failed');
        }
    }
);

// Async thunk to update email step one
export const updateEmailStepOne = createAsyncThunk(
    'auth/updateEmailStepOne',
    async (data: any, thunkAPI) => {
        try {
            const response = await api.post('/update-email-step-one', data);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Update email step one failed');
        }
    }
);

// Async thunk to update email step two
export const updateEmailStepTwo = createAsyncThunk(
    'auth/updateEmailStepTwo',
    async (data: any, thunkAPI) => {
        try {
            const response = await api.post('/update-email-step-two', data);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Update email step two failed');
        }
    }
);

// Async thunk to update email step three
export const updateEmailStepThree = createAsyncThunk(
    'auth/updateEmailStepThree',
    async (data: any, thunkAPI) => {
        try {
            const response = await api.post('/update-email-step-three', data);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Update email step three failed');
        }
    }
);

// Async thunk to update email step three
export const updateEmail = createAsyncThunk(
    'auth/updateEmail',
    async (data: any, thunkAPI) => {
        try {
            const response = await api.post('/update-email', data);
            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(error.response?.data || 'Update email failed');
        }
    }
);

const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchCaptcha.pending, (state) => {
                state.loadingCaptcha = true;
            })
            .addCase(fetchCaptcha.fulfilled, (state, action) => {
                state.loadingCaptcha = false;
                state.captcha = action.payload;
            })
            .addCase(fetchCaptcha.rejected, (state, action) => {
                state.loadingCaptcha = false;
                state.error = action.payload;
            })
            .addCase(login.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(login.fulfilled, (state, action) => {
                state.loading = false;
                state.isAuthenticated = true;
                state.userAuthId = action.payload.id || null;
                const decoded: any = jwtDecode(action.payload.token);
                state.user = decoded.user;
            })
            .addCase(login.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            .addCase(checkAuthStatus.pending, (state: any) => {
                state.loading = true;
            })
            .addCase(checkAuthStatus.fulfilled, (state: any, action: any) => {
                state.isAuthenticated = true;
                state.loading = false;
                state.user = action.payload.auth_data.user;
            })
            .addCase(checkAuthStatus.rejected, (state: any, action: any) => {
                state.loading = false;
                state.isAuthenticated = false;
                state.error = action.payload;
            })
            .addCase(getUserAuthId.fulfilled, (state, action) => {
                state.userAuthId = action.payload;
            })
            .addCase(logout.fulfilled, (state) => {
                state.isAuthenticated = false;
                state.userAuthId = null;
            })
            .addCase(logout.rejected, (state, action) => {
                state.error = action.payload as string;
            })
            .addCase(storeSubscriberStepOne.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeSubscriberStepOne.fulfilled, (state) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(storeSubscriberStepOne.rejected, (state: any, action: any) => {
                state.loading = false;
                state.error = action.payload.message as string;
            })
            .addCase(storeSubscriberStepTwo.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeSubscriberStepTwo.fulfilled, (state) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(storeSubscriberStepTwo.rejected, (state: any, action: any) => {
                state.loading = false;
                state.error = action.payload.message as string;
            }).addCase(storeSubscriberStepThree.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeSubscriberStepThree.fulfilled, (state) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(storeSubscriberStepThree.rejected, (state: any, action: any) => {
                state.loading = false;
                state.error = action.payload.message as string;
            }).addCase(storeSubscriberStepFour.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(storeSubscriberStepFour.fulfilled, (state) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(storeSubscriberStepFour.rejected, (state: any, action: any) => {
                state.loading = false;
                state.error = action.payload.message as string;
            })
            .addCase(resetPasswordStepOne.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(resetPasswordStepOne.fulfilled, (state) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(resetPasswordStepOne.rejected, (state: any, action: any) => {
                state.loading = false;
                state.error = action.payload.message as string;
            })
            .addCase(resetPasswordStepTwo.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(resetPasswordStepTwo.fulfilled, (state) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(resetPasswordStepTwo.rejected, (state: any, action: any) => {
                state.loading = false;
                state.error = action.payload.message as string;
            })
            .addCase(resetPassword.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(resetPassword.fulfilled, (state) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(resetPassword.rejected, (state: any, action: any) => {
                state.loading = false;
                state.error = action.payload.message as string;
            })
            ;
    },
});

export default authSlice.reducer;
